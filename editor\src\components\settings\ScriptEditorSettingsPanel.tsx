/**
 * 脚本编辑器设置面板
 * 提供脚本编辑器相关的设置选项
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Form,
  Switch,
  Slider,
  Select,
  Button,
  Space,
  Divider,
  Typography,
  InputNumber,
  message,
  Modal,
  List,
  Tag,
  Tooltip,
  Progress,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  SettingOutlined,
  ThunderboltOutlined,
  KeyOutlined,
  BgColorsOutlined,
  DatabaseOutlined,
  InfoCircleOutlined,
  ClearOutlined,
  ExportOutlined,
  ImportOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import KeyboardShortcutService, { KeyboardShortcut, ShortcutCategory } from '../../services/KeyboardShortcutService';
import ThemeService, { ThemeConfig } from '../../services/ThemeService';
import ScriptCacheService, { CacheStats, CacheConfig } from '../../services/ScriptCacheService';

const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface ScriptEditorSettingsPanelProps {
  visible: boolean;
  onClose: () => void;
}

const ScriptEditorSettingsPanel: React.FC<ScriptEditorSettingsPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('general');
  const [shortcuts, setShortcuts] = useState<KeyboardShortcut[]>([]);
  const [shortcutCategories, setShortcutCategories] = useState<ShortcutCategory[]>([]);
  const [themes, setThemes] = useState<ThemeConfig[]>([]);
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig | null>(null);
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
  const [cacheConfig, setCacheConfig] = useState<CacheConfig | null>(null);
  const [loading, setLoading] = useState(false);

  // 服务实例
  const keyboardService = KeyboardShortcutService.getInstance();
  const themeService = ThemeService.getInstance();
  const cacheService = ScriptCacheService.getInstance();

  useEffect(() => {
    if (visible) {
      loadSettings();
    }
  }, [visible]);

  const loadSettings = async () => {
    setLoading(true);
    try {
      // 加载快捷键设置
      const allShortcuts = keyboardService.getAllShortcuts();
      const categories = keyboardService.getAllCategories();
      setShortcuts(allShortcuts);
      setShortcutCategories(categories);

      // 加载主题设置
      const allThemes = themeService.getAllThemes();
      const current = themeService.getCurrentTheme();
      setThemes(allThemes);
      setCurrentTheme(current);

      // 加载缓存设置
      const stats = cacheService.getStats();
      const config = cacheService.getConfig();
      setCacheStats(stats);
      setCacheConfig(config);

      // 设置表单初始值
      form.setFieldsValue({
        theme: current?.id,
        autoSwitchTheme: false,
        cacheEnabled: true,
        cacheMaxSize: config.maxSize / (1024 * 1024), // 转换为MB
        cacheMaxEntries: config.maxEntries,
        cacheTTL: config.ttl / (1000 * 60 * 60), // 转换为小时
        compressionEnabled: config.compressionEnabled,
        persistToDisk: config.persistToDisk
      });
    } catch (error) {
      console.error('加载设置失败:', error);
      message.error('加载设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async (values: any) => {
    setLoading(true);
    try {
      // 保存主题设置
      if (values.theme !== currentTheme?.id) {
        themeService.setTheme(values.theme);
      }

      // 保存缓存设置
      if (cacheConfig) {
        const newCacheConfig = {
          ...cacheConfig,
          maxSize: values.cacheMaxSize * 1024 * 1024, // 转换为字节
          maxEntries: values.cacheMaxEntries,
          ttl: values.cacheTTL * 60 * 60 * 1000, // 转换为毫秒
          compressionEnabled: values.compressionEnabled,
          persistToDisk: values.persistToDisk
        };
        cacheService.updateConfig(newCacheConfig);
      }

      message.success('设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleShortcutToggle = (shortcutId: string) => {
    keyboardService.toggleShortcut(shortcutId);
    const updatedShortcuts = keyboardService.getAllShortcuts();
    setShortcuts(updatedShortcuts);
    message.success('快捷键设置已更新');
  };

  const handleClearCache = () => {
    Modal.confirm({
      title: '确认清空缓存',
      content: '这将清空所有脚本编译缓存，确定要继续吗？',
      onOk: () => {
        cacheService.clear();
        setCacheStats(cacheService.getStats());
        message.success('缓存已清空');
      }
    });
  };

  const handleExportSettings = () => {
    try {
      const settings = {
        shortcuts: shortcuts.map(s => ({ id: s.id, keys: s.keys, enabled: s.enabled })),
        theme: currentTheme?.id,
        cache: cacheService.exportCache()
      };
      
      const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'script-editor-settings.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      message.success('设置已导出');
    } catch (error) {
      console.error('导出设置失败:', error);
      message.error('导出设置失败');
    }
  };

  const renderGeneralSettings = () => (
    <Form form={form} layout="vertical" onFinish={handleSaveSettings}>
      <Title level={4}>
        <SettingOutlined /> 常规设置
      </Title>
      
      <Form.Item name="theme" label="主题">
        <Select placeholder="选择主题">
          {themes.map(theme => (
            <Option key={theme.id} value={theme.id}>
              <Space>
                <div
                  style={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: theme.colors.primary,
                    display: 'inline-block'
                  }}
                />
                {theme.name}
              </Space>
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="autoSwitchTheme" label="自动切换主题" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Divider />

      <Title level={4}>
        <ThunderboltOutlined /> 性能设置
      </Title>

      <Form.Item name="cacheEnabled" label="启用脚本缓存" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item name="cacheMaxSize" label="最大缓存大小 (MB)">
        <Slider min={10} max={500} marks={{ 10: '10MB', 100: '100MB', 500: '500MB' }} />
      </Form.Item>

      <Form.Item name="cacheMaxEntries" label="最大缓存条目数">
        <InputNumber min={100} max={10000} style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item name="cacheTTL" label="缓存生存时间 (小时)">
        <Slider min={1} max={168} marks={{ 1: '1h', 24: '24h', 168: '7d' }} />
      </Form.Item>

      <Form.Item name="compressionEnabled" label="启用压缩" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item name="persistToDisk" label="持久化到磁盘" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" loading={loading}>
            保存设置
          </Button>
          <Button onClick={() => form.resetFields()}>
            重置
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );

  const renderShortcutSettings = () => (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={4}>
          <KeyOutlined /> 快捷键设置
        </Title>
        <Button onClick={() => keyboardService.resetToDefaults()}>
          重置为默认
        </Button>
      </div>

      {shortcutCategories.map(category => (
        <Card key={category.id} title={category.name} style={{ marginBottom: 16 }}>
          <List
            dataSource={category.shortcuts}
            renderItem={shortcut => (
              <List.Item
                actions={[
                  <Switch
                    key="toggle"
                    checked={shortcut.enabled}
                    onChange={() => handleShortcutToggle(shortcut.id)}
                  />
                ]}
              >
                <List.Item.Meta
                  title={
                    <Space>
                      {shortcut.name}
                      <Space>
                        {shortcut.keys.map(key => (
                          <Tag key={key}>{key}</Tag>
                        ))}
                      </Space>
                    </Space>
                  }
                  description={shortcut.description}
                />
              </List.Item>
            )}
          />
        </Card>
      ))}
    </div>
  );

  const renderCacheSettings = () => (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={4}>
          <DatabaseOutlined /> 缓存管理
        </Title>
        <Space>
          <Button icon={<ClearOutlined />} onClick={handleClearCache}>
            清空缓存
          </Button>
          <Button icon={<ReloadOutlined />} onClick={loadSettings}>
            刷新
          </Button>
        </Space>
      </div>

      {cacheStats && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Statistic title="缓存条目" value={cacheStats.totalEntries} />
          </Col>
          <Col span={6}>
            <Statistic 
              title="缓存大小" 
              value={Math.round(cacheStats.totalSize / 1024 / 1024 * 100) / 100} 
              suffix="MB" 
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="命中率" 
              value={Math.round(cacheStats.hitRate * 100)} 
              suffix="%" 
            />
          </Col>
          <Col span={6}>
            <Statistic title="总命中数" value={cacheStats.totalHits} />
          </Col>
        </Row>
      )}

      {cacheStats && (
        <Card title="缓存性能" style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 16 }}>
            <Text>命中率</Text>
            <Progress 
              percent={Math.round(cacheStats.hitRate * 100)} 
              status={cacheStats.hitRate > 0.8 ? 'success' : cacheStats.hitRate > 0.5 ? 'normal' : 'exception'}
            />
          </div>
          
          <Row gutter={16}>
            <Col span={12}>
              <Statistic title="总命中数" value={cacheStats.totalHits} />
            </Col>
            <Col span={12}>
              <Statistic title="总未命中数" value={cacheStats.totalMisses} />
            </Col>
          </Row>
        </Card>
      )}

      <Card title="缓存配置">
        <Paragraph>
          <Text strong>最大大小:</Text> {cacheConfig && Math.round(cacheConfig.maxSize / 1024 / 1024)} MB
        </Paragraph>
        <Paragraph>
          <Text strong>最大条目数:</Text> {cacheConfig?.maxEntries}
        </Paragraph>
        <Paragraph>
          <Text strong>生存时间:</Text> {cacheConfig && Math.round(cacheConfig.ttl / 1000 / 60 / 60)} 小时
        </Paragraph>
        <Paragraph>
          <Text strong>压缩:</Text> {cacheConfig?.compressionEnabled ? '启用' : '禁用'}
        </Paragraph>
        <Paragraph>
          <Text strong>持久化:</Text> {cacheConfig?.persistToDisk ? '启用' : '禁用'}
        </Paragraph>
      </Card>
    </div>
  );

  return (
    <Modal
      title="脚本编辑器设置"
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="export" icon={<ExportOutlined />} onClick={handleExportSettings}>
          导出设置
        </Button>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              常规
            </span>
          }
          key="general"
        >
          {renderGeneralSettings()}
        </TabPane>

        <TabPane
          tab={
            <span>
              <KeyOutlined />
              快捷键
            </span>
          }
          key="shortcuts"
        >
          {renderShortcutSettings()}
        </TabPane>

        <TabPane
          tab={
            <span>
              <DatabaseOutlined />
              缓存
            </span>
          }
          key="cache"
        >
          {renderCacheSettings()}
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default ScriptEditorSettingsPanel;
