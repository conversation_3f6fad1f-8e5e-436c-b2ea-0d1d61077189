/**
 * 可视化脚本编辑器组件
 * 提供节点式的可视化脚本编辑功能
 */
import React, { useState, useRef, useCallback } from 'react';
import { 
  Button, 
  Space, 
  Tooltip, 
  Modal, 
  message, 
  Drawer,
  Typography,
  Card,
  List,
  Tag
} from 'antd';
import { 
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SaveOutlined,
  LoadingOutlined,
  PlusOutlined,
  SearchOutlined,
  SettingOutlined,
  BugOutlined,
  FullscreenOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import NodeSearch from '../visualscript/NodeSearch';

const { Title, Text } = Typography;

/**
 * 节点信息接口
 */
interface NodeInfo {
  type: string;
  label: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  tags: string[];
}

/**
 * 可视化脚本数据接口
 */
interface VisualScriptData {
  nodes: any[];
  connections: any[];
  variables: any[];
  customEvents: any[];
}

/**
 * 可视化脚本编辑器属性
 */
interface VisualScriptEditorProps {
  /** 脚本数据 */
  value?: VisualScriptData;
  /** 是否只读 */
  readOnly?: boolean;
  /** 高度 */
  height?: string | number;
  /** 内容变化回调 */
  onChange?: (value: VisualScriptData) => void;
  /** 节点选择回调 */
  onNodeSelect?: (nodeId: string) => void;
  /** 执行状态变化回调 */
  onExecutionStateChange?: (state: string) => void;
}

/**
 * 可视化脚本编辑器组件
 */
const VisualScriptEditor: React.FC<VisualScriptEditorProps> = ({
  value,
  readOnly = false,
  height = '500px',
  onChange,
  onNodeSelect,
  onExecutionStateChange
}) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  // 状态管理
  const [isExecuting, setIsExecuting] = useState(false);
  const [showNodeSearch, setShowNodeSearch] = useState(false);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [favoriteNodes, setFavoriteNodes] = useState<string[]>([]);
  const [recentNodes, setRecentNodes] = useState<string[]>([]);

  // 默认脚本数据
  const defaultScriptData: VisualScriptData = {
    nodes: [],
    connections: [],
    variables: [],
    customEvents: []
  };

  const scriptData = value || defaultScriptData;

  // 示例节点数据
  const availableNodes: NodeInfo[] = [
    {
      type: 'core/events/onStart',
      label: t('节点.开始事件'),
      description: t('节点.开始事件描述'),
      category: 'events',
      icon: 'play',
      color: '#52c41a',
      tags: ['事件', '生命周期']
    },
    {
      type: 'core/events/onUpdate',
      label: t('节点.更新事件'),
      description: t('节点.更新事件描述'),
      category: 'events',
      icon: 'sync',
      color: '#1890ff',
      tags: ['事件', '生命周期']
    },
    {
      type: 'core/debug/print',
      label: t('节点.打印'),
      description: t('节点.打印描述'),
      category: 'debug',
      icon: 'console',
      color: '#722ed1',
      tags: ['调试', '输出']
    },
    {
      type: 'core/math/add',
      label: t('节点.加法'),
      description: t('节点.加法描述'),
      category: 'math',
      icon: 'plus',
      color: '#fa8c16',
      tags: ['数学', '运算']
    },
    {
      type: 'core/flow/delay',
      label: t('节点.延迟'),
      description: t('节点.延迟描述'),
      category: 'flow',
      icon: 'clock',
      color: '#eb2f96',
      tags: ['流程', '时间']
    }
  ];

  // 处理脚本执行
  const handleExecute = useCallback(async () => {
    if (isExecuting) return;
    
    setIsExecuting(true);
    if (onExecutionStateChange) {
      onExecutionStateChange('running');
    }

    try {
      // 模拟脚本执行
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success(t('可视化脚本.执行成功'));
    } catch (error) {
      message.error(t('可视化脚本.执行失败'));
    } finally {
      setIsExecuting(false);
      if (onExecutionStateChange) {
        onExecutionStateChange('stopped');
      }
    }
  }, [isExecuting, onExecutionStateChange, t]);

  // 处理脚本停止
  const handleStop = useCallback(() => {
    setIsExecuting(false);
    if (onExecutionStateChange) {
      onExecutionStateChange('stopped');
    }
    message.info(t('可视化脚本.已停止'));
  }, [onExecutionStateChange, t]);

  // 处理节点添加
  const handleNodeAdd = useCallback((nodeType: string) => {
    const newNode = {
      id: `node_${Date.now()}`,
      type: nodeType,
      position: { x: 100, y: 100 },
      data: {}
    };

    const newScriptData = {
      ...scriptData,
      nodes: [...scriptData.nodes, newNode]
    };

    if (onChange) {
      onChange(newScriptData);
    }

    // 更新最近使用的节点
    const updatedRecentNodes = [nodeType, ...recentNodes.filter(n => n !== nodeType)].slice(0, 10);
    setRecentNodes(updatedRecentNodes);

    setShowNodeSearch(false);
    message.success(t('可视化脚本.节点已添加'));
  }, [scriptData, onChange, recentNodes, t]);

  // 处理收藏节点切换
  const handleToggleFavorite = useCallback((nodeType: string) => {
    const isFavorite = favoriteNodes.includes(nodeType);
    const updatedFavorites = isFavorite
      ? favoriteNodes.filter(n => n !== nodeType)
      : [...favoriteNodes, nodeType];
    
    setFavoriteNodes(updatedFavorites);
    message.success(isFavorite ? t('可视化脚本.已取消收藏') : t('可视化脚本.已收藏'));
  }, [favoriteNodes, t]);

  // 处理保存
  const handleSave = useCallback(() => {
    if (onChange) {
      onChange(scriptData);
    }
    message.success(t('可视化脚本.保存成功'));
  }, [scriptData, onChange, t]);

  // 渲染工具栏
  const renderToolbar = () => (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      padding: '8px 16px',
      borderBottom: '1px solid #f0f0f0',
      backgroundColor: '#fafafa'
    }}>
      <Space>
        <Tooltip title={t('可视化脚本.执行脚本')}>
          <Button
            type="primary"
            icon={isExecuting ? <LoadingOutlined /> : <PlayCircleOutlined />}
            onClick={handleExecute}
            disabled={isExecuting || readOnly}
            loading={isExecuting}
          >
            {t('可视化脚本.执行')}
          </Button>
        </Tooltip>
        
        <Tooltip title={t('可视化脚本.停止脚本')}>
          <Button
            icon={<StopOutlined />}
            onClick={handleStop}
            disabled={!isExecuting}
          >
            {t('可视化脚本.停止')}
          </Button>
        </Tooltip>
        
        <Tooltip title={t('可视化脚本.添加节点')}>
          <Button
            icon={<PlusOutlined />}
            onClick={() => setShowNodeSearch(true)}
            disabled={readOnly}
          >
            {t('可视化脚本.添加节点')}
          </Button>
        </Tooltip>
      </Space>
      
      <Space>
        <Tooltip title={t('可视化脚本.保存脚本')}>
          <Button
            icon={<SaveOutlined />}
            onClick={handleSave}
            disabled={readOnly}
          >
            {t('可视化脚本.保存')}
          </Button>
        </Tooltip>
        
        <Tooltip title={t('可视化脚本.设置')}>
          <Button
            type="text"
            icon={<SettingOutlined />}
          />
        </Tooltip>
        
        <Tooltip title={t('可视化脚本.全屏')}>
          <Button
            type="text"
            icon={<FullscreenOutlined />}
          />
        </Tooltip>
      </Space>
    </div>
  );

  // 渲染画布
  const renderCanvas = () => (
    <div style={{ 
      position: 'relative',
      width: '100%',
      height: 'calc(100% - 49px)',
      backgroundColor: '#f8f9fa',
      backgroundImage: `
        radial-gradient(circle, #ddd 1px, transparent 1px)
      `,
      backgroundSize: '20px 20px'
    }}>
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          cursor: 'crosshair'
        }}
      />
      
      {/* 空状态提示 */}
      {scriptData.nodes.length === 0 && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: '#999'
        }}>
          <Title level={4} type="secondary">
            {t('可视化脚本.空画布标题')}
          </Title>
          <Text type="secondary">
            {t('可视化脚本.空画布描述')}
          </Text>
          <br />
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setShowNodeSearch(true)}
            style={{ marginTop: '16px' }}
            disabled={readOnly}
          >
            {t('可视化脚本.添加第一个节点')}
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <div style={{ 
      height,
      border: '1px solid #d9d9d9',
      borderRadius: '6px',
      overflow: 'hidden',
      backgroundColor: '#fff'
    }}>
      {renderToolbar()}
      {renderCanvas()}
      
      {/* 节点搜索抽屉 */}
      <Drawer
        title={t('可视化脚本.选择节点')}
        placement="right"
        width={400}
        open={showNodeSearch}
        onClose={() => setShowNodeSearch(false)}
      >
        <NodeSearch
          nodes={availableNodes}
          favoriteNodes={favoriteNodes}
          recentNodes={recentNodes}
          onNodeSelect={handleNodeAdd}
          onToggleFavorite={handleToggleFavorite}
        />
      </Drawer>
    </div>
  );
};

export default VisualScriptEditor;
