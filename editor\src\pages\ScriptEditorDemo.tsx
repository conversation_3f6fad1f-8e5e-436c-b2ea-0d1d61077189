/**
 * 脚本编辑器演示页面
 * 展示脚本编辑器的各种功能
 */
import React, { useState } from 'react';
import { 
  Layout, 
  Card, 
  Row, 
  Col, 
  Typography, 
  Space, 
  Button, 
  Divider,
  Alert,
  Tabs
} from 'antd';
import { 
  CodeOutlined, 
  ApartmentOutlined, 
  PlayCircleOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import ScriptEditor from '../components/scripting/ScriptEditor';
import CodeEditor from '../components/scripting/CodeEditor';
import VisualScriptEditor from '../components/scripting/VisualScriptEditor';

const { Header, Content } = Layout;
const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

/**
 * 脚本编辑器演示页面
 */
const ScriptEditorDemo: React.FC = () => {
  // 状态管理
  const [scriptData, setScriptData] = useState({
    type: 'javascript',
    content: `// 示例JavaScript脚本
// 这是一个简单的玩家控制器脚本

let player = null;
let moveSpeed = 5.0;

function onStart() {
    console.log('玩家控制器初始化');
    
    // 获取玩家实体
    player = entity.getComponent('Transform');
    
    if (!player) {
        console.error('未找到玩家Transform组件');
        return;
    }
    
    console.log('玩家控制器准备就绪');
}

function onUpdate(deltaTime) {
    if (!player) return;
    
    // 获取输入
    const input = engine.getInputSystem();
    
    // 移动控制
    let moveX = 0;
    let moveZ = 0;
    
    if (input.isKeyPressed('W')) moveZ = 1;
    if (input.isKeyPressed('S')) moveZ = -1;
    if (input.isKeyPressed('A')) moveX = -1;
    if (input.isKeyPressed('D')) moveX = 1;
    
    // 应用移动
    if (moveX !== 0 || moveZ !== 0) {
        const moveDistance = moveSpeed * deltaTime;
        player.translate(moveX * moveDistance, 0, moveZ * moveDistance);
    }
}

function onDestroy() {
    console.log('玩家控制器销毁');
    player = null;
}

// 导出生命周期函数
export { onStart, onUpdate, onDestroy };`,
    enabled: true,
    autoRun: false,
    domain: 'default',
    variables: {}
  });

  const [codeEditorValue, setCodeEditorValue] = useState(`// 代码编辑器演示
console.log('Hello, DL Engine!');

// 这是一个简单的示例函数
function greetUser(name) {
    return \`Hello, \${name}! Welcome to DL Engine.\`;
}

// 调用函数
const message = greetUser('Developer');
console.log(message);`);

  const [visualScriptData, setVisualScriptData] = useState({
    nodes: [],
    connections: [],
    variables: [],
    customEvents: []
  });

  // 处理脚本数据变化
  const handleScriptDataChange = (newData: any) => {
    setScriptData(newData);
    console.log('脚本数据已更新:', newData);
  };

  // 渲染功能介绍
  const renderFeatureIntro = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
      <Col span={8}>
        <Card 
          title={
            <Space>
              <CodeOutlined />
              代码编辑器
            </Space>
          }
          size="small"
        >
          <Paragraph>
            支持JavaScript和TypeScript的代码编辑，提供语法高亮、
            智能提示、代码折叠等功能。
          </Paragraph>
          <ul>
            <li>语法高亮</li>
            <li>自动完成</li>
            <li>代码折叠</li>
            <li>全屏编辑</li>
          </ul>
        </Card>
      </Col>
      
      <Col span={8}>
        <Card 
          title={
            <Space>
              <ApartmentOutlined />
              可视化脚本
            </Space>
          }
          size="small"
        >
          <Paragraph>
            节点式的可视化编程界面，通过拖拽和连接节点来创建脚本逻辑。
          </Paragraph>
          <ul>
            <li>节点式编程</li>
            <li>拖拽连接</li>
            <li>实时预览</li>
            <li>调试支持</li>
          </ul>
        </Card>
      </Col>
      
      <Col span={8}>
        <Card 
          title={
            <Space>
              <FileTextOutlined />
              脚本模板
            </Space>
          }
          size="small"
        >
          <Paragraph>
            预定义的脚本模板，帮助开发者快速开始脚本开发。
          </Paragraph>
          <ul>
            <li>基础模板</li>
            <li>游戏逻辑模板</li>
            <li>UI管理模板</li>
            <li>自定义模板</li>
          </ul>
        </Card>
      </Col>
    </Row>
  );

  // 渲染使用说明
  const renderUsageGuide = () => (
    <Alert
      message="使用说明"
      description={
        <div>
          <p><strong>1. 选择脚本类型：</strong>在工具栏中选择JavaScript、TypeScript或可视化脚本。</p>
          <p><strong>2. 编写代码：</strong>在编辑器中编写您的脚本代码或创建可视化节点。</p>
          <p><strong>3. 执行脚本：</strong>点击执行按钮运行脚本，查看控制台输出。</p>
          <p><strong>4. 保存脚本：</strong>点击保存按钮保存您的脚本。</p>
          <p><strong>5. 使用模板：</strong>点击模板按钮选择预定义的脚本模板。</p>
        </div>
      }
      type="info"
      showIcon
      style={{ marginBottom: 24 }}
    />
  );

  return (
    <Layout style={{ minHeight: '100vh', backgroundColor: '#f0f2f5' }}>
      <Header style={{ backgroundColor: '#fff', padding: '0 24px' }}>
        <Title level={2} style={{ margin: 0, lineHeight: '64px' }}>
          <Space>
            <PlayCircleOutlined />
            脚本编辑器演示
          </Space>
        </Title>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        <div style={{ maxWidth: 1200, margin: '0 auto' }}>
          {/* 功能介绍 */}
          {renderFeatureIntro()}
          
          {/* 使用说明 */}
          {renderUsageGuide()}
          
          {/* 主要演示区域 */}
          <Card title="完整脚本编辑器" style={{ marginBottom: 24 }}>
            <ScriptEditor
              data={scriptData}
              onChange={handleScriptDataChange}
              entityId="demo-entity"
            />
          </Card>
          
          {/* 组件演示 */}
          <Card title="组件演示">
            <Tabs defaultActiveKey="code-editor">
              <TabPane 
                tab={
                  <span>
                    <CodeOutlined />
                    代码编辑器
                  </span>
                } 
                key="code-editor"
              >
                <Paragraph>
                  独立的代码编辑器组件，支持多种编程语言和编辑功能。
                </Paragraph>
                <CodeEditor
                  value={codeEditorValue}
                  language="javascript"
                  height="300px"
                  onChange={setCodeEditorValue}
                  showLineNumbers={true}
                  enableAutoComplete={true}
                  enableCodeFolding={true}
                />
              </TabPane>
              
              <TabPane 
                tab={
                  <span>
                    <ApartmentOutlined />
                    可视化脚本编辑器
                  </span>
                } 
                key="visual-editor"
              >
                <Paragraph>
                  可视化脚本编辑器，通过节点和连接创建脚本逻辑。
                </Paragraph>
                <VisualScriptEditor
                  value={visualScriptData}
                  height="400px"
                  onChange={setVisualScriptData}
                />
              </TabPane>
            </Tabs>
          </Card>
          
          {/* 技术信息 */}
          <Card title="技术信息" style={{ marginTop: 24 }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Title level={4}>支持的脚本类型</Title>
                <ul>
                  <li><Text code>JavaScript</Text> - 标准JavaScript脚本</li>
                  <li><Text code>TypeScript</Text> - 类型安全的TypeScript脚本</li>
                  <li><Text code>Visual Script</Text> - 节点式可视化脚本</li>
                </ul>
              </Col>
              
              <Col span={12}>
                <Title level={4}>集成功能</Title>
                <ul>
                  <li>与DL引擎深度集成</li>
                  <li>实时脚本编译和执行</li>
                  <li>调试和错误检查</li>
                  <li>脚本模板系统</li>
                </ul>
              </Col>
            </Row>
            
            <Divider />
            
            <Title level={4}>当前脚本状态</Title>
            <pre style={{ 
              backgroundColor: '#f5f5f5', 
              padding: '16px', 
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              {JSON.stringify(scriptData, null, 2)}
            </pre>
          </Card>
        </div>
      </Content>
    </Layout>
  );
};

export default ScriptEditorDemo;
