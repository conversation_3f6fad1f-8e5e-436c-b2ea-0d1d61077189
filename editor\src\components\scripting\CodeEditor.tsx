/**
 * 代码编辑器组件
 * 提供语法高亮、智能提示等功能
 */
import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { Button, Select, Space, Tooltip, message, AutoComplete, List, Popover } from 'antd';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  CopyOutlined,
  DownloadOutlined,
  UploadOutlined,
  BulbOutlined,
  SearchOutlined,
  SettingOutlined,
  FormatPainterOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Option } = Select;

/**
 * 脚本语言类型
 */
export enum ScriptLanguage {
  JAVASCRIPT = 'javascript',
  TYPESCRIPT = 'typescript',
  JSON = 'json'
}

/**
 * 代码编辑器属性
 */
interface CodeEditorProps {
  /** 代码内容 */
  value: string;
  /** 语言类型 */
  language: ScriptLanguage;
  /** 是否只读 */
  readOnly?: boolean;
  /** 是否显示行号 */
  showLineNumbers?: boolean;
  /** 是否启用代码折叠 */
  enableCodeFolding?: boolean;
  /** 是否启用自动完成 */
  enableAutoComplete?: boolean;
  /** 主题 */
  theme?: 'light' | 'dark';
  /** 高度 */
  height?: string | number;
  /** 内容变化回调 */
  onChange?: (value: string) => void;
  /** 光标位置变化回调 */
  onCursorChange?: (line: number, column: number) => void;
}

/**
 * 代码编辑器组件
 */
// 智能提示数据
const getAutoCompleteData = (language: ScriptLanguage) => {
  const commonKeywords = {
    javascript: [
      'function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'do', 'switch', 'case', 'break', 'continue',
      'return', 'try', 'catch', 'finally', 'throw', 'new', 'this', 'typeof', 'instanceof', 'in', 'delete',
      'true', 'false', 'null', 'undefined', 'console', 'document', 'window', 'Array', 'Object', 'String',
      'Number', 'Boolean', 'Date', 'Math', 'JSON', 'Promise', 'async', 'await'
    ],
    typescript: [
      'interface', 'type', 'class', 'extends', 'implements', 'public', 'private', 'protected', 'readonly',
      'static', 'abstract', 'enum', 'namespace', 'module', 'import', 'export', 'default', 'as', 'from',
      'string', 'number', 'boolean', 'any', 'void', 'never', 'unknown', 'object'
    ]
  };

  const dlEngineAPI = [
    'entity', 'engine', 'transform', 'rigidbody', 'collider', 'renderer', 'camera', 'light',
    'onStart', 'onUpdate', 'onDestroy', 'onCollisionEnter', 'onCollisionExit', 'onTriggerEnter',
    'Vector3', 'Quaternion', 'Matrix4', 'Color', 'Material', 'Texture', 'Mesh', 'Animation',
    'InputSystem', 'PhysicsSystem', 'RenderSystem', 'AudioSystem', 'SceneManager', 'AssetManager'
  ];

  return [
    ...commonKeywords.javascript,
    ...(language === ScriptLanguage.TYPESCRIPT ? commonKeywords.typescript : []),
    ...dlEngineAPI
  ];
};

const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  language,
  readOnly = false,
  showLineNumbers = true,
  enableCodeFolding = true,
  enableAutoComplete = true,
  theme = 'light',
  height = '400px',
  onChange,
  onCursorChange
}) => {
  const { t } = useTranslation();
  const editorRef = useRef<HTMLTextAreaElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [cursorPosition, setCursorPosition] = useState({ line: 1, column: 1 });
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [suggestionPosition, setSuggestionPosition] = useState({ top: 0, left: 0 });
  const [currentWord, setCurrentWord] = useState('');

  // 获取智能提示数据
  const autoCompleteData = useMemo(() => getAutoCompleteData(language), [language]);

  // 获取当前光标位置的单词
  const getCurrentWord = useCallback((text: string, cursorPos: number) => {
    const beforeCursor = text.substring(0, cursorPos);
    const afterCursor = text.substring(cursorPos);

    // 找到单词边界
    const wordStart = beforeCursor.search(/\w+$/);
    const wordEnd = afterCursor.search(/\W/);

    if (wordStart === -1) return '';

    const start = wordStart;
    const end = wordEnd === -1 ? afterCursor.length : wordEnd;

    return beforeCursor.substring(start) + afterCursor.substring(0, end);
  }, []);

  // 获取智能提示建议
  const getSuggestions = useCallback((word: string) => {
    if (!word || word.length < 2) return [];

    return autoCompleteData.filter(item =>
      item.toLowerCase().startsWith(word.toLowerCase())
    ).slice(0, 10);
  }, [autoCompleteData]);

  // 处理内容变化
  const handleChange = useCallback((event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = event.target.value;
    const cursorPos = event.target.selectionStart;

    if (onChange) {
      onChange(newValue);
    }

    // 智能提示逻辑
    if (enableAutoComplete && !readOnly) {
      const word = getCurrentWord(newValue, cursorPos);
      setCurrentWord(word);

      if (word.length >= 2) {
        const suggestionList = getSuggestions(word);
        setSuggestions(suggestionList);

        if (suggestionList.length > 0) {
          // 计算提示框位置
          const textarea = event.target;
          const rect = textarea.getBoundingClientRect();
          const lineHeight = 21; // 估算行高
          const charWidth = 8; // 估算字符宽度

          const lines = newValue.substring(0, cursorPos).split('\n');
          const currentLine = lines.length - 1;
          const currentColumn = lines[lines.length - 1].length;

          setSuggestionPosition({
            top: rect.top + (currentLine * lineHeight) + lineHeight,
            left: rect.left + (currentColumn * charWidth)
          });

          setShowSuggestions(true);
        } else {
          setShowSuggestions(false);
        }
      } else {
        setShowSuggestions(false);
      }
    }
  }, [onChange, enableAutoComplete, readOnly, getCurrentWord, getSuggestions]);

  // 处理光标位置变化
  const handleCursorChange = () => {
    if (editorRef.current) {
      const textarea = editorRef.current;
      const text = textarea.value;
      const cursorPos = textarea.selectionStart;
      
      // 计算行号和列号
      const lines = text.substring(0, cursorPos).split('\n');
      const line = lines.length;
      const column = lines[lines.length - 1].length + 1;
      
      setCursorPosition({ line, column });
      
      if (onCursorChange) {
        onCursorChange(line, column);
      }
    }
  };

  // 复制代码
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(value);
      message.success(t('代码编辑器.复制成功'));
    } catch (error) {
      message.error(t('代码编辑器.复制失败'));
    }
  };

  // 下载代码
  const handleDownload = () => {
    const blob = new Blob([value], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `script.${language === ScriptLanguage.TYPESCRIPT ? 'ts' : 'js'}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    message.success(t('代码编辑器.下载成功'));
  };

  // 上传代码
  const handleUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.js,.ts,.json';
    input.onchange = (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          if (onChange) {
            onChange(content);
          }
          message.success(t('代码编辑器.上传成功'));
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // 切换全屏
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 获取语言显示名称
  const getLanguageDisplayName = (lang: ScriptLanguage) => {
    switch (lang) {
      case ScriptLanguage.JAVASCRIPT:
        return 'JavaScript';
      case ScriptLanguage.TYPESCRIPT:
        return 'TypeScript';
      case ScriptLanguage.JSON:
        return 'JSON';
      default:
        return lang;
    }
  };

  // 渲染工具栏
  const renderToolbar = () => (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      padding: '8px 12px',
      borderBottom: '1px solid #f0f0f0',
      backgroundColor: '#fafafa'
    }}>
      <Space>
        <span style={{ fontSize: '12px', color: '#666' }}>
          {getLanguageDisplayName(language)}
        </span>
        <span style={{ fontSize: '12px', color: '#999' }}>
          行 {cursorPosition.line}, 列 {cursorPosition.column}
        </span>
      </Space>
      
      <Space>
        <Tooltip title={t('代码编辑器.复制代码')}>
          <Button 
            type="text" 
            size="small" 
            icon={<CopyOutlined />} 
            onClick={handleCopy}
          />
        </Tooltip>
        
        <Tooltip title={t('代码编辑器.下载代码')}>
          <Button 
            type="text" 
            size="small" 
            icon={<DownloadOutlined />} 
            onClick={handleDownload}
          />
        </Tooltip>
        
        <Tooltip title={t('代码编辑器.上传代码')}>
          <Button 
            type="text" 
            size="small" 
            icon={<UploadOutlined />} 
            onClick={handleUpload}
            disabled={readOnly}
          />
        </Tooltip>
        
        <Tooltip title={isFullscreen ? t('代码编辑器.退出全屏') : t('代码编辑器.全屏')}>
          <Button 
            type="text" 
            size="small" 
            icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />} 
            onClick={toggleFullscreen}
          />
        </Tooltip>
      </Space>
    </div>
  );

  return (
    <div 
      style={{ 
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        overflow: 'hidden',
        height: isFullscreen ? '100vh' : height,
        position: isFullscreen ? 'fixed' : 'relative',
        top: isFullscreen ? 0 : 'auto',
        left: isFullscreen ? 0 : 'auto',
        right: isFullscreen ? 0 : 'auto',
        bottom: isFullscreen ? 0 : 'auto',
        zIndex: isFullscreen ? 9999 : 'auto',
        backgroundColor: '#fff'
      }}
    >
      {renderToolbar()}
      
      <textarea
        ref={editorRef}
        value={value}
        onChange={handleChange}
        onSelect={handleCursorChange}
        onKeyUp={handleCursorChange}
        onClick={handleCursorChange}
        readOnly={readOnly}
        style={{
          width: '100%',
          height: 'calc(100% - 41px)',
          border: 'none',
          outline: 'none',
          resize: 'none',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
          fontSize: '14px',
          lineHeight: '1.5',
          padding: '12px',
          backgroundColor: theme === 'dark' ? '#1e1e1e' : '#fff',
          color: theme === 'dark' ? '#d4d4d4' : '#333',
          tabSize: 2
        }}
        placeholder={t('代码编辑器.占位符') || '在这里编写您的代码...'}
        spellCheck={false}
      />
    </div>
  );
};

export default CodeEditor;
