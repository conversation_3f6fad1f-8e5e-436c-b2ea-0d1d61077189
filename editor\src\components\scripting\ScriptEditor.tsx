/**
 * 脚本编辑器
 * 提供完整的脚本编辑功能，包括代码编辑和可视化脚本编辑
 */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Switch,
  Select,
  Form,
  message,
  Modal,
  Tooltip,
  Divider,
  Typography,
  Alert
} from 'antd';
import {
  PlayCircleOutlined,
  StopOutlined,
  SaveOutlined,
  LoadingOutlined,
  CodeOutlined,
  BugOutlined,
  SettingOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ScriptService, { ScriptType as ServiceScriptType } from '../../services/ScriptService';
import CodeEditor, { ScriptLanguage } from './CodeEditor';
import VisualScriptEditor from './VisualScriptEditor';
import ScriptTemplates from './ScriptTemplates';

const { TabPane } = Tabs;
const { Option } = Select;
const { Text } = Typography;

/**
 * 脚本类型
 */
enum ScriptType {
  JAVASCRIPT = 'javascript',
  TYPESCRIPT = 'typescript',
  VISUAL_SCRIPT = 'visual_script'
}

/**
 * 脚本执行状态
 */
enum ScriptExecutionState {
  STOPPED = 'stopped',
  RUNNING = 'running',
  PAUSED = 'paused',
  ERROR = 'error'
}

/**
 * 脚本编辑器属性
 */
interface ScriptEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
  /** 实体ID */
  entityId?: string;
}

/**
 * 脚本数据接口
 */
interface ScriptData {
  /** 脚本类型 */
  type: ScriptType;
  /** 脚本内容 */
  content: string;
  /** 是否启用 */
  enabled: boolean;
  /** 是否自动运行 */
  autoRun: boolean;
  /** 脚本域 */
  domain: string;
  /** 可视化脚本数据 */
  visualScript?: any;
  /** 脚本变量 */
  variables?: Record<string, any>;
}

/**
 * 脚本编辑器组件
 */
const ScriptEditor: React.FC<ScriptEditorProps> = ({ data, onChange }) => {
  const { t } = useTranslation();

  // 状态管理
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState<string>('code');
  const [scriptData, setScriptData] = useState<ScriptData>({
    type: ScriptType.JAVASCRIPT,
    content: '',
    enabled: true,
    autoRun: false,
    domain: 'default',
    variables: {}
  });
  const [executionState, setExecutionState] = useState<ScriptExecutionState>(ScriptExecutionState.STOPPED);
  const [isLoading, setIsLoading] = useState(false);
  const [debugMode, setDebugMode] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);

  // 引用
  const scriptService = ScriptService.getInstance();

  // 初始化数据
  useEffect(() => {
    if (data) {
      const initialData: ScriptData = {
        type: data.type || ScriptType.JAVASCRIPT,
        content: data.content || getDefaultScriptContent(data.type || ScriptType.JAVASCRIPT),
        enabled: data.enabled !== undefined ? data.enabled : true,
        autoRun: data.autoRun !== undefined ? data.autoRun : false,
        domain: data.domain || 'default',
        visualScript: data.visualScript,
        variables: data.variables || {}
      };
      setScriptData(initialData);
      form.setFieldsValue(initialData);
    }
  }, [data, form]);

  /**
   * 获取默认脚本内容
   */
  const getDefaultScriptContent = (type: ScriptType): string => {
    switch (type) {
      case ScriptType.JAVASCRIPT:
        return `// JavaScript脚本
// 在这里编写您的脚本代码

// 生命周期函数
function onStart() {
    console.log('脚本开始执行');
}

function onUpdate(deltaTime) {
    // 每帧更新
}

function onDestroy() {
    console.log('脚本销毁');
}

// 导出生命周期函数
export { onStart, onUpdate, onDestroy };`;

      case ScriptType.TYPESCRIPT:
        return `// TypeScript脚本
// 在这里编写您的脚本代码

import { Entity, Component } from 'dl-engine';

// 生命周期函数
export function onStart(): void {
    console.log('脚本开始执行');
}

export function onUpdate(deltaTime: number): void {
    // 每帧更新
}

export function onDestroy(): void {
    console.log('脚本销毁');
}`;

      case ScriptType.VISUAL_SCRIPT:
        return '// 可视化脚本 - 请使用可视化编辑器';

      default:
        return '';
    }
  };

  /**
   * 处理脚本数据变化
   */
  const handleScriptDataChange = useCallback((field: keyof ScriptData, value: any) => {
    const newData = { ...scriptData, [field]: value };
    setScriptData(newData);

    if (onChange) {
      onChange(newData);
    }
  }, [scriptData, onChange]);

  /**
   * 处理脚本内容变化
   */
  const handleContentChange = useCallback((content: string) => {
    handleScriptDataChange('content', content);
  }, [handleScriptDataChange]);

  /**
   * 处理脚本类型变化
   */
  const handleTypeChange = useCallback((type: ScriptType) => {
    const newContent = getDefaultScriptContent(type);
    setScriptData(prev => ({
      ...prev,
      type,
      content: newContent
    }));

    if (onChange) {
      onChange({
        ...scriptData,
        type,
        content: newContent
      });
    }
  }, [scriptData, onChange]);

  /**
   * 执行脚本
   */
  const handleExecuteScript = useCallback(async () => {
    if (!scriptData.content.trim() && scriptData.type !== ScriptType.VISUAL_SCRIPT) {
      message.warning(t('脚本编辑器.内容为空'));
      return;
    }

    setIsLoading(true);
    setExecutionState(ScriptExecutionState.RUNNING);

    try {
      // 创建临时脚本数据
      const tempScriptData = {
        id: `temp_${Date.now()}`,
        name: 'Temporary Script',
        type: scriptData.type === ScriptType.TYPESCRIPT ? ServiceScriptType.TYPESCRIPT :
              scriptData.type === ScriptType.VISUAL_SCRIPT ? ServiceScriptType.VISUAL_SCRIPT :
              ServiceScriptType.JAVASCRIPT,
        content: scriptData.content,
        enabled: true,
        autoRun: false,
        domain: scriptData.domain,
        visualScript: scriptData.visualScript,
        variables: scriptData.variables,
        lastModified: new Date()
      };

      // 注册并执行脚本
      scriptService.registerScript(tempScriptData);
      await scriptService.compileScript(tempScriptData.id);
      const success = await scriptService.executeScript(tempScriptData.id);

      if (success) {
        message.success(t('脚本编辑器.执行成功'));
      } else {
        message.error(t('脚本编辑器.执行失败'));
        setExecutionState(ScriptExecutionState.ERROR);
      }

      // 清理临时脚本
      scriptService.removeScript(tempScriptData.id);
    } catch (error) {
      console.error('脚本执行错误:', error);
      message.error(t('脚本编辑器.执行失败'));
      setExecutionState(ScriptExecutionState.ERROR);
    } finally {
      setIsLoading(false);
    }
  }, [scriptData, scriptService, t]);

  /**
   * 停止脚本执行
   */
  const handleStopScript = useCallback(() => {
    setExecutionState(ScriptExecutionState.STOPPED);
    message.info(t('脚本编辑器.已停止'));
  }, [t]);

  /**
   * 保存脚本
   */
  const handleSaveScript = useCallback(() => {
    if (onChange) {
      onChange(scriptData);
      message.success(t('脚本编辑器.保存成功'));
    }
  }, [scriptData, onChange, t]);

  /**
   * 处理模板选择
   */
  const handleTemplateSelect = useCallback((template: any) => {
    const newData = {
      ...scriptData,
      type: template.type === 'typescript' ? ScriptType.TYPESCRIPT : ScriptType.JAVASCRIPT,
      content: template.content
    };
    setScriptData(newData);

    if (onChange) {
      onChange(newData);
    }

    message.success(`已应用模板: ${template.name}`);
  }, [scriptData, onChange]);

  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <div style={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: '8px 16px',
      borderBottom: '1px solid #f0f0f0'
    }}>
      <Space>
        <Select
          value={scriptData.type}
          onChange={handleTypeChange}
          style={{ width: 120 }}
        >
          <Option value={ScriptType.JAVASCRIPT}>JavaScript</Option>
          <Option value={ScriptType.TYPESCRIPT}>TypeScript</Option>
          <Option value={ScriptType.VISUAL_SCRIPT}>{t('脚本编辑器.可视化脚本')}</Option>
        </Select>

        <Divider type="vertical" />

        <Tooltip title={t('脚本编辑器.执行脚本')}>
          <Button
            type="primary"
            icon={isLoading ? <LoadingOutlined /> : <PlayCircleOutlined />}
            onClick={handleExecuteScript}
            disabled={isLoading || !scriptData.enabled}
            loading={isLoading}
          >
            {t('脚本编辑器.执行')}
          </Button>
        </Tooltip>

        <Tooltip title={t('脚本编辑器.停止脚本')}>
          <Button
            icon={<StopOutlined />}
            onClick={handleStopScript}
            disabled={executionState === ScriptExecutionState.STOPPED}
          >
            {t('脚本编辑器.停止')}
          </Button>
        </Tooltip>

        <Tooltip title={t('脚本编辑器.保存脚本')}>
          <Button
            icon={<SaveOutlined />}
            onClick={handleSaveScript}
          >
            {t('脚本编辑器.保存')}
          </Button>
        </Tooltip>

        <Tooltip title={t('脚本编辑器.选择模板')}>
          <Button
            icon={<FileTextOutlined />}
            onClick={() => setShowTemplates(true)}
          >
            {t('脚本编辑器.模板')}
          </Button>
        </Tooltip>
      </Space>

      <Space>
        <Switch
          checked={debugMode}
          onChange={setDebugMode}
          checkedChildren={<BugOutlined />}
          unCheckedChildren={<BugOutlined />}
        />
        <Text type="secondary">{t('脚本编辑器.调试模式')}</Text>

        <Button
          type="text"
          icon={<SettingOutlined />}
          onClick={() => setShowSettings(true)}
        />
      </Space>
    </div>
  );

  /**
   * 渲染代码编辑器
   */
  const renderCodeEditor = () => {
    const language = scriptData.type === ScriptType.TYPESCRIPT
      ? ScriptLanguage.TYPESCRIPT
      : ScriptLanguage.JAVASCRIPT;

    return (
      <CodeEditor
        value={scriptData.content}
        language={language}
        readOnly={!scriptData.enabled}
        height="400px"
        onChange={handleContentChange}
        showLineNumbers={true}
        enableAutoComplete={true}
        enableCodeFolding={true}
      />
    );
  };

  /**
   * 渲染可视化脚本编辑器
   */
  const renderVisualScriptEditor = () => (
    <VisualScriptEditor
      value={scriptData.visualScript}
      readOnly={!scriptData.enabled}
      height="400px"
      onChange={(visualScript) => {
        handleScriptDataChange('visualScript', visualScript);
      }}
      onExecutionStateChange={(state) => {
        setExecutionState(state as ScriptExecutionState);
      }}
    />
  );

  /**
   * 渲染脚本设置
   */
  const renderScriptSettings = () => (
    <Form
      form={form}
      layout="vertical"
      initialValues={scriptData}
      onValuesChange={(changedValues) => {
        Object.entries(changedValues).forEach(([field, value]) => {
          handleScriptDataChange(field as keyof ScriptData, value);
        });
      }}
    >
      <Form.Item label={t('脚本编辑器.启用脚本')} name="enabled" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label={t('脚本编辑器.自动运行')} name="autoRun" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item label={t('脚本编辑器.脚本域')} name="domain">
        <Select>
          <Option value="default">{t('脚本编辑器.默认域')}</Option>
          <Option value="ui">{t('脚本编辑器.UI域')}</Option>
          <Option value="physics">{t('脚本编辑器.物理域')}</Option>
          <Option value="audio">{t('脚本编辑器.音频域')}</Option>
        </Select>
      </Form.Item>
    </Form>
  );

  /**
   * 渲染执行状态
   */
  const renderExecutionStatus = () => {
    let statusColor = 'default';
    let statusText = t('脚本编辑器.状态.已停止');

    switch (executionState) {
      case ScriptExecutionState.RUNNING:
        statusColor = 'processing';
        statusText = t('脚本编辑器.状态.运行中');
        break;
      case ScriptExecutionState.PAUSED:
        statusColor = 'warning';
        statusText = t('脚本编辑器.状态.已暂停');
        break;
      case ScriptExecutionState.ERROR:
        statusColor = 'error';
        statusText = t('脚本编辑器.状态.错误');
        break;
    }

    return (
      <Alert
        message={statusText}
        type={statusColor as any}
        showIcon
        style={{ marginBottom: '16px' }}
      />
    );
  };

  /**
   * 渲染设置模态框
   */
  const renderSettingsModal = () => (
    <Modal
      title={t('脚本编辑器.脚本设置')}
      open={showSettings}
      onCancel={() => setShowSettings(false)}
      footer={[
        <Button key="cancel" onClick={() => setShowSettings(false)}>
          {t('通用.取消')}
        </Button>,
        <Button key="ok" type="primary" onClick={() => setShowSettings(false)}>
          {t('通用.确定')}
        </Button>
      ]}
    >
      {renderScriptSettings()}
    </Modal>
  );

  return (
    <Card
      title={t('脚本编辑器.标题')}
      size="small"
      style={{ height: '100%' }}
      styles={{ body: { padding: 0, height: 'calc(100% - 57px)' } }}
    >
      {renderToolbar()}
      {renderExecutionStatus()}

      <div style={{ padding: '16px', height: 'calc(100% - 120px)' }}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          style={{ height: '100%' }}
        >
          <TabPane
            tab={
              <span>
                <CodeOutlined />
                {scriptData.type === ScriptType.VISUAL_SCRIPT
                  ? t('脚本编辑器.可视化脚本')
                  : t('脚本编辑器.代码编辑器')
                }
              </span>
            }
            key="code"
          >
            {scriptData.type === ScriptType.VISUAL_SCRIPT
              ? renderVisualScriptEditor()
              : renderCodeEditor()
            }
          </TabPane>

          <TabPane
            tab={
              <span>
                <SettingOutlined />
                {t('脚本编辑器.设置')}
              </span>
            }
            key="settings"
          >
            {renderScriptSettings()}
          </TabPane>

          {debugMode && (
            <TabPane
              tab={
                <span>
                  <BugOutlined />
                  {t('脚本编辑器.调试')}
                </span>
              }
              key="debug"
            >
              <div style={{ padding: '16px' }}>
                <Text type="secondary">{t('脚本编辑器.调试功能开发中')}</Text>
              </div>
            </TabPane>
          )}
        </Tabs>
      </div>

      {renderSettingsModal()}

      {/* 脚本模板选择 */}
      <ScriptTemplates
        visible={showTemplates}
        onClose={() => setShowTemplates(false)}
        onSelectTemplate={handleTemplateSelect}
      />
    </Card>
  );
};

export default ScriptEditor;
